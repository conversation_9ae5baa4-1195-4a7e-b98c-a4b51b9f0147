# recallloop-be-ui

This [RecallLoop Admin](https://github.com/CitrusInformatics-India/recallloop-be-ui.git) project was generated with [create refine-app](https://github.com/refinedev/refine/tree/master/packages/create-refine-app).

## Getting Started
This repository contains the RecallLoop Admin Panel files and documents.

To start with this repository, first clone the repo to the local:

```bash
    git clone https://github.com/CitrusInformatics-India/recallloop-be-ui.git
```

## Available Scripts

### Running the development server.

```bash
    npm run dev
```

### Building for production.

```bash
    npm run build
```

### Running the production server.

```bash
    npm run start
```

## License

MIT
