"use client";
import React from "react";
import { Card, Button } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";

interface ProfileCardProps {
  id: string;
  name: string;
  title: string;
  location: string;
  avatar?: string;
}

const UserCard: React.FC<ProfileCardProps> = ({
  id,
  name,
  title,
  location,
  avatar,
}) => {
  const router = useRouter();

  const handleViewProfile = () => {
    router.push(`/profile/${id}`);
  };

  return (
    <Card
      hoverable
      style={{
        width: "100%",
        borderRadius: 12,
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
        border: "1px solid #f0f0f0",
        backgroundColor: "white",
      }}
      bodyStyle={{
        padding: "24px",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <div style={{ flex: 1 }}>
        <h3
          style={{
            margin: 0,
            fontSize: "18px",
            fontWeight: 600,
            color: "#262626",
            marginBottom: 4,
          }}
        >
          {name}
        </h3>
        <p
          style={{
            margin: 0,
            fontSize: "14px",
            color: "#8c8c8c",
            marginBottom: 2,
          }}
        >
          {title}
        </p>
        <p
          style={{
            margin: 0,
            fontSize: "14px",
            color: "#8c8c8c",
            marginBottom: 16,
          }}
        >
          {location}
        </p>

        <Button
          type="default"
          onClick={handleViewProfile}
          style={{
            borderColor: "#7c5cfc",
            color: "#7c5cfc",
            borderRadius: 6,
            fontWeight: 500,
            fontSize: "14px",
            height: "32px",
            paddingLeft: "16px",
            paddingRight: "16px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#7c5cfc";
            e.currentTarget.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
            e.currentTarget.style.color = "#7c5cfc";
          }}
        >
          View Profile
        </Button>
      </div>

      <div style={{ marginLeft: 20 }}>
        {avatar ? (
          <img
            src={avatar}
            alt={name}
            style={{
              width: 100,
              height: 100,
              borderRadius: 12,
              objectFit: "cover",
            }}
          />
        ) : (
          <div
            style={{
              width: 100,
              height: 100,
              borderRadius: 12,
              backgroundColor: "#f0f0f0",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <UserOutlined style={{ fontSize: 24, color: "#8c8c8c" }} />
          </div>
        )}
      </div>
    </Card>
  );
};

export default UserCard;
