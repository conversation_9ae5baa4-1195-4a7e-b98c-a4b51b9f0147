import { Button, Modal } from "antd";
import { useState } from "react";
import { PoweroffOutlined } from "@ant-design/icons";

interface LogoutDialogProps {
  onLogout: () => void;
  color?:string;

}

const LogoutDialog: React.FC<LogoutDialogProps> = ({ onLogout,color }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    onLogout();
    localStorage.clear();
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <Button
        type="text"
        icon={<PoweroffOutlined />}
        onClick={showModal}
        style={{ color: color?color: "red" }}
      >
        Logout
      </Button>
      <Modal
        title="Logout Confirmation"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="Yes"
        cancelText="No"
      >
        <p>Are you sure you want to logout?</p>
      </Modal>
    </>
  );
};

export default LogoutDialog; 