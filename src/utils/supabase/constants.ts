import { StatCardProps } from "@components/card/graphCards/StatCard";

export const SUPABASE_URL = "https://iwdfzvfqbtokqetmbmbp.supabase.co";
export const SUPABASE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlhdCI6MTYzMDU2NzAxMCwiZXhwIjoxOTQ2MTQzMDEwfQ._gr6kXGkQBi9BM9dx5vKaNKYj_DJN1xlkarprGpM_fU";
// Mock data for charts
export const tenantData = [
  { month: "Jan", value: 140 },
  { month: "Feb", value: 120 },
  { month: "Mar", value: 160 },
  { month: "Apr", value: 130 },
  { month: "May", value: 150 },
  { month: "Jun", value: 120 },
];

export const caregiverData = [
  { month: "Jan", value: 35 },
  { month: "Feb", value: 42 },
  { month: "Mar", value: 38 },
  { month: "Apr", value: 45 },
  { month: "May", value: 40 },
  { month: "Jun", value: 45 },
];

export const userData = [
  { month: "Jan", value: 250 },
  { month: "Feb", value: 280 },
  { month: "Mar", value: 290 },
  { month: "Apr", value: 300 },
];
export const statCards: StatCardProps[] = [
  {
    title: "Total Tenants",
    value: 120,
    percentage: 85,
    status: "down",
    color: "#ef4444",
    chartType: "line",
    chartData: tenantData,
  },
  {
    title: "Total Caregivers",
    value: 45,
    percentage: 92,
    status: "up",
    color: "#10b981",
    chartType: "line",
    chartData: caregiverData,
  },
  {
    title: "Total Users",
    value: 300,
    percentage: 78,
    status: "up",
    color: "#6366f1",
    chartType: "progress",
    chartData: userData,
  },
];
export const tenants = [
  { label: "Tenant 1", value: "tenant1" },
  { label: "Tenant 2", value: "tenant2" },
  { label: "Tenant 3", value: "tenant3" },
];

export const categories = [
  { label: "Action", value: "action" },
  { label: "Puzzle", value: "puzzle" },
  { label: "Adventure", value: "adventure" },
];
export const tenantNames = ["John Doe", "Jane Doe", "Michael Johnson"];
export const games = [
  {
    image: "assets/catan.jpeg",
    title: "Catan",
    description: "Strategy board game",
    players: 824,
  },
  {
    image: "assets/chess.jpeg",
    title: "Chess",
    description: "Classic strategy game",
    players: 523,
  },
  {
    image: "assets/uno.jpeg",
    title: "UNO",
    description: "Card matching game",
    players: 824,
  },
  {
    image: "assets/monoploy.jpeg",
    title: "Monopoly",
    description: "Economic strategy game",
    players: 824,
  },
  {
    image: "assets/scrabble.jpeg",
    title: "Scrabble",
    description: "Word-building game",
    players: 824,
  },
  {
    image: "assets/humanity.jpeg",
    title: "Cards Ag.Humanity",
    description: "Party card game",
    players: 824,
  },
];
export const criteriaData = [
  {
    key: "1",
    criteria: "Play for 3 days",
    description:
      "Consistency is key! You've shown dedication by playing for 3 days in a row. Keep the momentum going and aim for an even longer streak!",
    points: "Star",
  },
  {
    key: "2",
    criteria: "Play for 5 days",
    description:
      "You're picking up speed! Playing 5 days in a row shows real commitment. Keep it going — you're building a powerful habit.",
    points: "Superstar",
  },
  {
    key: "3",
    criteria: "Play for 7 days",
    description:
      "A full week of dedication! You've proven your focus and drive by showing up every day. You're on a roll — don't stop now!",
    points: "Champion",
  },
  {
    key: "4",
    criteria: "Play for 31 days",
    description:
      "An entire month of unstoppable progress! Your 31-day streak is a testament to your discipline and passion. You've earned legendary status.",
    points: "Icon",
  },
];
export const mockData = [
  {
    id: "001",
    avatarColor: "#6366f1",
    name: "ABC Tech World",
    email: "<EMAIL>",
    status: true,
    caretakers: 5,
    players: 30,
  },
  {
    id: "002",
    avatarColor: "#f472b6",
    name: "Stratify Partners",
    email: "<EMAIL>",
    status: true,
    caretakers: 8,
    players: 92,
  },
  {
    id: "003",
    avatarColor: "#2dd4bf",
    name: "Elevance Group",
    email: "<EMAIL>",
    status: true,
    caretakers: 10,
    players: 192,
  },
  {
    id: "004",
    avatarColor: "#8b5cf6",
    name: "AxisPoint Solutions",
    email: "<EMAIL>",
    status: true,
    caretakers: 21,
    players: 103,
  },
  {
    id: "005",
    avatarColor: "#fb923c",
    name: "SustainaCore",
    email: "<EMAIL>",
    status: true,
    caretakers: 15,
    players: 88,
  },
  {
    id: "006",
    avatarColor: "#a3a3a3",
    name: "MediNova",
    email: "<EMAIL>",
    status: true,
    caretakers: 6,
    players: 30,
  },
  {
    id: "007",
    avatarColor: "#18181b",
    name: "Zenith Health",
    email: "<EMAIL>",
    status: true,
    caretakers: 18,
    players: 75,
  },
];
//export const ROLE = "tenant";
export const ROLE = 'caregiver';
// export const ROLE = 'superadmin';
export const caregiversData = [
  { month: "Jan", value: 120 },
  { month: "Feb", value: 135 },
  { month: "Mar", value: 150 },
  { month: "Apr", value: 165 },
  { month: "May", value: 180 },
  { month: "Jun", value: 200 },
];

export const playersData = [
  { month: "Jan", value: 450 },
  { month: "Feb", value: 490 },
  { month: "Mar", value: 530 },
  { month: "Apr", value: 580 },
  { month: "May", value: 620 },
  { month: "Jun", value: 670 },
];

export const gamesPlayedTodayData = [
  { month: "Jun 17", value: 320 },
  { month: "Jun 18", value: 340 },
  { month: "Jun 19", value: 310 },
  { month: "Jun 20", value: 360 },
  { month: "Jun 21", value: 400 },
  { month: "Jun 22", value: 450 },
];

export const usersData = [
  {
    id: "1",
    name: "Alice Johnson",
    avatar: "/avatars/alice.jpg",
    role: "User",
  },
  { id: "2", name: "Bob Smith", avatar: "/avatars/bob.jpg", role: "User" },
  {
    id: "3",
    name: "Catherine Green",
    avatar: "/avatars/catherine.jpg",
    role: "User",
  },
  { id: "4", name: "David Wilson", avatar: "/avatars/david.jpg", role: "User" },
  {
    id: "5",
    name: "Evelyn Brooks",
    avatar: "/avatars/evelyn.jpg",
    role: "User",
  },
];

// Sample data for caregivers
export const caregiversDatas = [
  {
    id: "1",
    name: "John Doe",
    title: "Software Engineer",
    location: "San Francisco, CA",
    avatar: "/avatars/M1.jpg",
    gender: "Male",
  },
  {
    id: "2",
    name: "Jane Smith",
    title: "Project Manager",
    location: "New York, NY",
    avatar: "/avatars/M2.jpg",
    gender: "Female",
  },
  {
    id: "3",
    name: "Mike Johnson",
    title: "UI Designer",
    location: "Austin, TX",
    avatar: "/avatars/M4.jpg",
    gender: "Male",
  },
  {
    id: "4",
    name: "Emma Davis",
    title: "Data Scientist",
    location: "Seattle, WA",
    avatar: "/avatars/L1.jpg",
    gender: "Female",
  },
  {
    id: "5",
    name: "Chris Lee",
    title: "Frontend Developer",
    location: "Chicago, IL",
    avatar: "/avatars/L2.jpg",
    gender: "Female",
  },
  {
    id: "6",
    name: "Sophia Brown",
    title: "HR Specialist",
    location: "Los Angeles, CA",
    avatar: "/avatars/L3.jpg",
    gender: "Female",
  },
];
export const PLAYER = "player";
export const CAREGIVER = "caregiver";
export const TENANT = "tenant";
export const TENANTADM = "tenant";
export const SUPERADMIN = "superadmin";
export const USER = "user";
