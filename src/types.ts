// export type PartyType = {
//   id: string;
//   name: string;
//   description?: string;
//   is_tenant?: boolean;
//   is_active: boolean;
//   is_deleted: boolean;
//   created_at: string;
//   updated_at: string;
// };

// export type Party = {
//   id: string;
//   person_id: string;
//   party_type_id: string;
//   name: string;
//   description?: string;
//   is_active: boolean;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type Person = {
//   id: string;
//   first_name: string;
//   last_name: string;
//   display_name: string;
//   phone_number: string;
//   date_of_birth: string;
//   gender: string;
//   contact_info: string;
//   avatar_url: string;
//   preferences: string;
//   timezone: string;
//   locale: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type UserAccount = {
//   id: string;
//   party_id: string;
//   username: string;
//   email: string;
//   password_hash: string;
//   is_active: string;
//   is_verified: string;
//   verification_token: string;
//   reset_token: string;
//   reset_token_expires: string;
//   last_login: string;
//   login_attempts: string;
//   locked_until: string
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type GameCategory = {
//   id: string;
//   name: string;
//   description?: string;
//   icon_url?: string;
//   sort_order: number;
//   is_active: boolean;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type Role = {
//   id: string;
//   party_id: string;
//   role_name: string;
//   display_name: string;
//   description: string;
//   permissions: string;
//   is_system_role: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type PartyRelationShip = {
//   id: string;
//   parent_party_id: string;
//   child_party_id: string;
//   relationship_type: string;
//   is_active: boolean;
//   valid_from: string;
//   valid_to: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type PartyRole = {
//   id: string;
//   party_id: string;
//   role_id: string;
//   valid_from: string;
//   valid_to: string;
//   is_active: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type GameAssignment = {
//   id: string;
//   caregiver_party_id: string;
//   player_party_id: string;
//   game_id: string;
//   status: string;
//   due_date: string;
//   notes: string;
//   assigned_at: string;
//   completed_at: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type GamePlayHistory = {
//   id: string;
//   player_party_id: string;
//   game_id: string;
//   assignment_id: string;
//   session_id: string;
//   played_at: string;
//   completed_at: string;
//   score: number;
//   max_score: number;
//   duration_sec: number;
//   level_reached: number;
//   achievements: string;
//   game_data: string;
//   device_info: string;
//   is_completed: boolean;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type Player_Points = {
//   id: string;
//   player_party_id: string;
//   tenant_party_id: string;
//   total_points:  number;
//   available_points: number;
//   lifetime_points: number;
//   last_updated: string;
//   is_deleted: boolean;
//   created_by: number;
//   updated_by: number;
//   created_at: string;
//   updated_at: string;
// };

// export type PlayerTransaction = {
//   id: string;
//   player_party_id: string;
//   tenant_party_id: string;
//   transaction_type: string;
//   points_change: number;
//   reason: string;
//   reference_id: string;
//   reference_type: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type Reward = {
//   id: string;
//   party_id: string;
//   category: string;
//   name: string;
//   description: string;
//   points_required: number;
//   max_redemptions: number;
//   total_available: number;
//   image_url: string;
//   is_active: boolean;
//   valid_from: string;
//   valid_to: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };

// export type RewardRedemption = {
//   id: string;
//   player_party_id: string;
//   reward_id: string;
//   points_spent: string;
//   status: string;
//   redemption_code: string;
//   notes: string;
//   approved_by: string;
//   approved_at: string;
//   delivered_at: string;
//   is_deleted: boolean;
//   created_by: string;
//   updated_by: string;
//   created_at: string;
//   updated_at: string;
// };
import { Dayjs } from "dayjs";
export type ManageUserRequest = {
  bio: string;
  date_of_birth: string | null;
  first_name: string;
  gender: string;
  is_login_enabled: boolean;
  last_name: string;
  party_id: string;
  party_type_key: string;
  phone_number: string;
  profile_url: string;
  role_id?: string;
  tenant_code?: string;
};
export type AddTenantRequest = {
  name: string;
  mobile: string;
  address: string;
  email?: string | null;
  tenant_code: string;
  image_url: string | null;
};
export type ListTenantResponse = {
  player_count: number;
  caregiver_count: number;
  created_at: string;
  tenant_code: string;
  phone_number: string;
  avatar_url: string | null;
  caretakers: number;
  email: string;
  id: string;
  players: number;
  status: boolean;
  tenant_name: string;
}[];
export type Tenant = {
  id: string | number;
  name: string;
  email?: string;
  avatarColor?: string;
  status?: boolean;
  caretakers?: number;
  players?: number;
  phone?: string;
  tenantCode: string;
  avatar_url: string;
};

export type ListCategoryResponse = {
  value: string | number;
  label: string;
};
export type CategoryOption = {
  value: string;
  label: string;
};

export type GameFormData = {
  category: string;
  title: string;
  description: string;
  no_of_levels: number;
  tenants: string[];
  valid_from: Dayjs;
  valid_upto: Dayjs;
};
export type DashboardSummaryParams = {
  tenant_code: string;
};
export type CategoryItem = {
  id: string;
  name: string;
};

export type RewardCategoryItem = {
  id: string;
  name: string;
  description?: string;
  image_url: string | null;
  // Add other fields as they become available from the API
};

export type CreateRewardRequest = {
  category_id: string;
  name: string;
  points: number;
  activity_type: string;
  description?: string;
};

export type RewardFormValues = {
  category: string;
  name: string;
  points: string | number;
  activityType: string;
  description?: string;
};

export type RewardItem = {
  id: string;
  name: string;
  category_name: string;
  category_id: string;
  points: number;
  activity_type: string;
  description?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
};

export type TenantOption = {
  label: string;
  value: string;
  key?: string;
  title?: string;
};

export type ListTenantsResponse = {
  data: {
    data: ListTenantResponse;
  };
};

export type ListCategoriesResponse = {
  data: CategoryItem[];
  valid_from: Dayjs;
  valid_upto: Dayjs;
  id: string | number;
  name: string;
  email?: string;
  avatarColor?: string;
  status?: boolean;
  caretakers?: number;
  players?: number;
};
export type ManageUserFormValues = {
  firstName?: string;
  lastName?: string;
  dob?: string | { format: (fmt: string) => string }; // Accepts string or dayjs-like object
  gender?: string;
  email?: string;
  countryCode?: string;
  phone?: string;
  profileImage?: File | null;
  loginRequired?: boolean;
  password?: string;
  role_id?: string;
  tenantCode?: string;
  username: string;
  bio: string;
};
export type TenantFormValues = {
  name: string;
  mobile: string;
  address: string;
  email?: string | null;
  tenant_email?: string | null;
  tenant_code: string;
  image_url: string | null;
  firstName?: string;
  lastName?: string;
  countryCode?: string;
  phone?: string;
  username: string;
  password?: string;
};
// User Info API Types
export type GetUserInfoRequest = {
  user_id: string;
};
export type DashboardOverviewResponse = {
  data: {
    overview: {
      total_users: {
        count: number;
      };
      total_players: {
        count: number;
      };
      total_tenants: {
        count: number;
      };
      total_caregivers: {
        count: number;
      };
    };
  };
  success: boolean;
};

export type Game = {
  id: string;
  game_id: string;
  game_name: string;
  description: string;
  thumbnail_url: string;
  category_id: string;
  category_name?: string;
  no_of_levels?: number;
  valid_from: string;
  valid_to: string;
  created_at: string;
  updated_at: string;
  version?: string;
  selected?: boolean; // For UI state management
  game_tags?: string[];
  is_active?: boolean;
  difficulty_level?: number;
  estimated_duration_sec?: number;
  assigned_parties?: string[];
};

export type FetchGamesResponse = {
  data: Game[];
};
export type AssignGamesRequest = {
  party_ids: string[];
  // tenant_id: string;
  game_ids: string[];
  due_date: string | null;
  notes: string | null;
};
export type ListTenantUsersRequest = {
  party_id: string | "";
  tenant_code: string | "";
  party_type_key: string | "";
};
export type ListTenantGamesRequest = {
  tenant_code: string;
};
export type ListUnassignedTenantsResponse = {
  data: ListTenantResponse | ListTenantResponse[];
};

export type RoleType = {
  id: string;
  role_name: string;
  display_name: string;
};

export type ProfileDetailsData = {
  bio: string | null;
  name: string;
  last_name: string;
  avatar_url: string | null;
  first_name: string;
  games_played_count: number;
  assigned_games_count: number;
};

export type ProfileDetailsResponse = {
  data: ProfileDetailsData;
  status: string;
};

export type UserInfoData = {
  tenant_code: string | null;
  id: string;
  party_type_key: string;
};

export type GetUserInfoResponse = {
  party_type_key: string;
  id: string;
  parent_party_id: string;
  data: UserInfoData;
};
export type ProfileData = {
  bio: string | null;
  name: string;
  last_name: string;
  avatar_url: string | null;
  first_name: string;
  games_played_count: number;
  assigned_games_count: number;
};

export type UserData = {
  id: string;
  email: string;
  user_metadata: {
    first_name: string;
    last_name: string;
    username: string;
    email: string;
  };
};
export type CareGiverResponse = {
  party_id: string;
  child_name: string;
  description: string;
};
export type NewTenantFormValues = {
  name: string;
  mobile: string;
  address: string;
  email?: string | null;
  tenant_code: string;
  image_url: string | null;
  firstName?: string;
  lastName?: string;
  countryCode?: string;
  phone?: string;
  username: string;
  password?: string;
};

export interface SignUpUserParams {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  username: string;
  tenant_code: string;
  party_id: string;
  avatar_url: string | null;
  party_type_key: string;
  rolename: string;
}

export interface SignUpUserResponse {
  user: {
    id: string;
    email: string;
    [key: string]: unknown; // in case more fields are returned
  };
}

export type GetUserDashboardRequest = {
  tenant_id: string;
  party_id: string;
  party_type_key: string;
};

export type ErrorCatch = {
  details?: string;
  message?: string;
  Error?: string;
};
export type AssignedGame = {
  assigned_parties: string[];
  id: string;
  title: string;
  date_assigned: string;
  status?: string;
};
