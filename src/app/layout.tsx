// app/layout.tsx
import { Metadata } from "next";
import { cookies } from "next/headers";
import React from "react";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import "@refinedev/antd/dist/reset.css";

import { ROLE } from "@utils/supabase/constants"; // Replace with actual cookie/session role logic
import RefineApp from "./refineApp";

export const metadata: Metadata = {
  title: "RecallLoop",
  description: "Generated by create refine app",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const theme = cookieStore.get("theme");
  const defaultMode = theme?.value === "dark" ? "dark" : "light";

  // Example: you might want to read role from cookie or Supabase session
  const role = ROLE ?? "user";

  return (
    <html lang="en">
      <body>
        <AntdRegistry>
          <RefineApp defaultMode={defaultMode} role={role}>
            {children}
          </RefineApp>
        </AntdRegistry>
      </body>
    </html>
  );
}
