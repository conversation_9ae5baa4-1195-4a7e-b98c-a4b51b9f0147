"use client";

import {
  <PERSON>,
  Avatar,
  But<PERSON>,
  Typo<PERSON>,
  Row,
  Col,
  Progress,
  Divider,
  Space,
  Spin,
  message,
  Upload,
  Modal,
} from "antd";
import {
  MailOutlined,
  UserOutlined,
  EditOutlined,
  CameraOutlined,
} from "@ant-design/icons";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { LineChart, Line, ResponsiveContainer } from "recharts";
import { dataProviderInstance } from "@providers/data-provider";
import { supabaseClient } from "@utils/supabase/client";

import { ERROR_MESSAGES } from "@utils/supabase/messages";
import { ProfileData, UserData } from "@types";
import LogoutDialog from "@components/dialog/logoutDialog";
import { authProviderClient } from "@providers/auth-provider/auth-provider.client";

const { Title, Text } = Typography;

const assignedGamesChartData = [
  { value: 20 },
  { value: 15 },
  { value: 10 },
  { value: 5 },
  { value: 12 },
  { value: 10 },
  { value: 12 },
];

const Profile: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  const fetchProfileData = async () => {
    try {
      setLoading(true);

      const userResponse = await dataProviderInstance.getUser();
      const userId = userResponse.data?.id;

      if (!userId) {
        throw new Error("User not found. Please login again.");
      }

      if (!userResponse.data || !userResponse.data?.user_metadata) {
        throw new Error("Incomplete user data received from backend.");
      }
      setUserData({
        id: userResponse.data.id,
        email: userResponse.data?.email || "",
        user_metadata: {
          first_name: userResponse.data?.user_metadata?.first_name || "",
          last_name: userResponse.data?.user_metadata?.last_name || "",
          username: userResponse.data?.user_metadata?.username || "",
          email: userResponse.data?.user_metadata?.email || "",
        },
      });

      const profileResponse = await dataProviderInstance.getProfileDetails({
        party_id: userId,
      });

      console.log(profileResponse);
      if (
        profileResponse.data &&
        profileResponse.data.status === "success" &&
        profileResponse.data.data
      ) {
        setProfileData(profileResponse.data.data);
      } else {
        throw new Error("Failed to fetch profile details");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("Error message:", errorMessage);
      message.error(ERROR_MESSAGES.load_profile);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  const handleLogoutClick = async () => {
    try {
      authProviderClient.logout({});
      localStorage.clear();
      sessionStorage.clear();
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      router.push("/login");
    }
  };

  const handleContactClick = () => {
    //
  };

  const handleAvatarUpload = async (file: File) => {
    try {
      setUploadingAvatar(true);

      const fileName = `avatars/${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 24 * 365);

      if (signedUrlError) throw signedUrlError;

      const avatarUrl = signedUrlData?.signedUrl;

      if (!avatarUrl) {
        throw new Error("Failed to get avatar URL");
      }

      await dataProviderInstance.updateProfileInfo({
        user_id: userData!.id,
        avatar_url: avatarUrl,
      });

      setProfileData((prev) =>
        prev ? { ...prev, avatar_url: avatarUrl } : null
      );

      message.success("Avatar updated successfully!");
      setShowAvatarModal(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update avatar";
      message.error(errorMessage);
    } finally {
      setUploadingAvatar(false);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      handleAvatarUpload(file);
      return false;
    },
    showUploadList: false,
  };

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (!profileData || !userData) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <Text>Failed to load profile data</Text>
      </div>
    );
  }

  const stats = [
    {
      title: "Assigned Games",
      value: profileData.assigned_games_count,
      change: -12.5,
      chartType: "line",
      data: assignedGamesChartData,
    },
    {
      title: "Games Played",
      value: profileData.games_played_count,
      change: 78,
      chartType: "progress",
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress",
    },
    {
      title: "Workshops Attended",
      value: 15,
      change: -75,
      chartType: "progress",
    },
  ];

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 12,
        boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
        padding: 32,
      }}
    >
      <Card
        style={{ borderRadius: 16 }}
        cover={
          <img
            alt="banner"
            src="/assets/profilebanner.jpeg"
            style={{
              height: 160,
              objectFit: "cover",
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
            }}
          />
        }
      >
        <div
          style={{ textAlign: "center", marginTop: -64, position: "relative" }}
        >
          <div style={{ position: "relative", display: "inline-block" }}>
            <Avatar
              size={186}
              src={profileData.avatar_url}
              style={{ backgroundColor: "#7c3aed" }}
              icon={!profileData.avatar_url && <UserOutlined />}
            />
            <Button
              shape="circle"
              icon={<CameraOutlined />}
              size="small"
              style={{
                position: "absolute",
                bottom: 8,
                right: 8,
                background: "#ffffff",
                boxShadow: "0 2px 6px rgba(0,0,0,0.2)",
                border: "2px solid #fff",
              }}
              onClick={() => setShowAvatarModal(true)}
            />
          </div>
          <Title level={3} style={{ marginTop: 12 }}>
            {profileData.name ||
              `${profileData.first_name} ${profileData.last_name}`}
          </Title>
          <Text type="secondary">{userData.user_metadata.email}</Text>
          <div style={{ marginTop: 12 }}>
            {/* <Text>Bio: {profileData.bio || "No bio available"}</Text> */}
          </div>

          <Button
            icon={<MailOutlined />}
            type="primary"
            block
            style={{ marginTop: 24 }}
            onClick={handleContactClick}
          >
            Contact
          </Button>
        </div>

        <Divider />

        <Row gutter={16}>
          {stats.map((item, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Text type="secondary">{item.title}</Text>
                  <Title level={3} style={{ margin: 0 }}>
                    {item.value}
                  </Title>

                  {item.chartType === "progress" ? (
                    <Progress
                      type="dashboard"
                      percent={
                        item.title === "Completion Rate"
                          ? item.value
                          : Math.abs(item.change)
                      }
                      strokeColor={item.change < 0 ? "red" : "green"}
                      size={60}
                    />
                  ) : (
                    <>
                      <Text type="danger" style={{ fontSize: 14 }}>
                        ▼ {Math.abs(item.change)}%
                      </Text>
                      <div style={{ height: 40 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={item.data}>
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="red"
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </>
                  )}
                </Space>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Logout Dialog */}

        <Button
          // icon={<LogoutOutlined />}
          type="primary"
          block
          style={{ marginTop: 32, color: "#fff" }}
        >
          <LogoutDialog color="#fff" onLogout={handleLogoutClick} />
          {/* Logout */}
        </Button>
      </Card>

      <Modal
        title="Update Profile Picture"
        open={showAvatarModal}
        onCancel={() => setShowAvatarModal(false)}
        footer={null}
        width={400}
      >
        <div style={{ textAlign: "center", padding: "20px 0" }}>
          <Upload {...uploadProps}>
            <Button
              icon={<EditOutlined />}
              type="primary"
              size="large"
              loading={uploadingAvatar}
              style={{ marginBottom: 16 }}
            >
              {uploadingAvatar ? "Uploading..." : "Choose Image"}
            </Button>
          </Upload>
          <Text type="secondary" style={{ display: "block", fontSize: 12 }}>
            Supported formats: JPG, PNG, GIF (Max 5MB)
          </Text>
        </div>
      </Modal>
    </div>
  );
};

export default Profile;
