"use client";

import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Card,
  Typography,
  Row,
  Col,
  message
} from "antd";

const { Title } = Typography;
const { TextArea } = Input;

// Mock data - replace with actual data from your API
const gameAssignments = [
  { label: "Memory Challenge", value: "memory-challenge" },
  { label: "Word Puzzle", value: "word-puzzle" },
  { label: "Math Quiz", value: "math-quiz" },
  { label: "Pattern Recognition", value: "pattern-recognition" },
  { label: "Logic Games", value: "logic-games" },
];

const ageOptions = [
  { label: "18-25", value: "18-25" },
  { label: "26-35", value: "26-35" },
  { label: "36-45", value: "36-45" },
  { label: "46-55", value: "46-55" },
  { label: "56-65", value: "56-65" },
  { label: "65+", value: "65+" },
];

export default function AddCaregiverPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: unknown) => {
    setLoading(true);
    try {
      // Handle form submission here
      console.log('Form values:', values);
      message.success('Caregiver added successfully!');
      form.resetFields();
    } catch (error) {
      console.log(error)
      message.error('Failed to add caregiver. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onReset = () => {
    form.resetFields();
  };

  return (
    <div style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 8,

        
    }}>
      <Card
       
      >
        {/* Header */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "32px",
          paddingBottom: "16px",
          
        }}>
          <Title level={3} style={{ margin: 0, fontWeight: 600 }}>
            Add Caregiver
          </Title>
          <Button
            type="text"
            onClick={() => window.history.back()}
            style={{ color: "#666" }}
          >
            Cancel
          </Button>
        </div>

        {/* Form */}
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ width: "100%" }}
        >
          <Title level={5} style={{ marginBottom: "24px", color: "#333" }}>
            Player Information
          </Title>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Name"
                name="name"
                rules={[{ required: true, message: 'Please enter caregiver name' }]}
              >
                <Input
                  placeholder="Caregiver's name"
                  style={{
                    borderRadius: "4px",
                    height: "40px"
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Age"
                name="age"
                rules={[{ required: true, message: 'You have to select your age' }]}
              >
                <Select
                  placeholder="Select your age"
                  style={{ height: "40px" }}
                  options={ageOptions}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Game Assignments"
                name="gameAssignments"
                rules={[{ required: true, message: 'You have to select at least one game assignment' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select game assignments"
                  style={{ minHeight: "40px" }}
                  options={gameAssignments}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Team Name"
                name="teamName"
              >
                <Input
                  placeholder="Add team name"
                  style={{
                    borderRadius: "4px",
                    height: "40px"
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  { required: true, message: 'Please enter a valid email' },
                  { type: 'email', message: 'Please enter a valid email format' }
                ]}
              >
                <Input
                  placeholder="Add email"
                  style={{
                    borderRadius: "4px",
                    height: "40px"
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Phone Number"
                name="phoneNumber"
                rules={[{ required: true, message: 'Please enter a valid phone number' }]}
              >
                <Input
                  placeholder="Add phone number"
                  style={{
                    borderRadius: "4px",
                    height: "40px"
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Caregiver Bio"
            name="caregiverBio"
          >
            <TextArea
              placeholder="Add bio..."
              rows={4}
              style={{
                borderRadius: "4px",
                resize: "none"
              }}
            />
          </Form.Item>

          {/* Action Buttons */}
            <div style={{
            display: "flex",
            gap: "12px",
            marginTop: "32px",
            paddingTop: "24px",
            justifyContent: "flex-end"
            }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
              backgroundColor: "#6366f1",
              borderColor: "#6366f1",
              borderRadius: "4px",
              height: "40px",
              fontSize: "14px",
              fontWeight: 500,
              paddingLeft: "24px",
              paddingRight: "24px"
              }}
            >
              Submit
            </Button>
            <Button
              onClick={onReset}
              style={{
              borderRadius: "4px",
              height: "40px",
              fontSize: "14px",
              paddingLeft: "24px",
              paddingRight: "24px"
              }}
            >
              Reset
            </Button>
            </div>
        </Form>
      </Card>
    </div>
  );
}
