import { EyeFilled, UserOutlined, TeamOutlined } from "@ant-design/icons";
import { Tenant } from "@types";
import { Button, Switch, Avatar, Tag, Typography } from "antd";

const { Text } = Typography;

export const getColumns = (currentPage: number, pageSize: number, onViewTenant?: (tenant: Tenant) => void) => [
  {
    title: "#",
    key: "siNo",
    width: 60,
    render: (_: Tenant, __: Tenant, index: number) => (
      <div style={{
        width: "32px",
        height: "32px",
        borderRadius: "50%",
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        color: "white",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontSize: "12px",
        fontWeight: 600,
      }}>
        {(currentPage - 1) * pageSize + index + 1}
      </div>
    ),
  },
  {
    title: "Tenant",
    key: "tenant_info",
    width: 250,
    render: (record: Tenant) => (
      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        <Avatar
          src={record.avatar_url}
          size={40}
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          }}
          onError={() => true}
        >
          {record.name.charAt(0).toUpperCase()}
        </Avatar>
        <div>
          <Text style={{
            fontWeight: 600,
            color: "#374151",
            fontSize: "14px",
            display: "block",
          }}>
            {record.name}
          </Text>
          <Text style={{
            color: "#6b7280",
            fontSize: "12px",
          }}>
            {record.email || "No email"}
          </Text>
        </div>
      </div>
    ),
  },
  {
    title: "Contact Info",
    key: "contact_info",
    width: 200,
    render: (record: Tenant) => (
      <div>
        <div style={{
          padding: "4px 8px",
          background: "rgba(102, 126, 234, 0.1)",
          borderRadius: "12px",
          marginBottom: "4px",
          display: "inline-block",
        }}>
          <Text style={{
            color: "#667eea",
            fontSize: "12px",
            fontWeight: 600,
          }}>
            {record.tenantCode}
          </Text>
        </div>
        <Text style={{
          color: "#6b7280",
          fontSize: "12px",
          display: "block",
        }}>
          {record.phone || "No phone"}
        </Text>
      </div>
    ),
  },
  {
    title: "Caregivers",
    dataIndex: "caretakers",
    key: "caretakers",
    width: 120,
    render: (count: number) => (
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "8px 12px",
        background: "rgba(102, 126, 234, 0.1)",
        borderRadius: "20px",
        justifyContent: "center",
      }}>
        <TeamOutlined style={{ color: "#667eea", fontSize: "14px" }} />
        <Text style={{
          color: "#667eea",
          fontSize: "16px",
          fontWeight: 700,
        }}>
          {count || 0}
        </Text>
      </div>
    ),
  },
  {
    title: "Players",
    dataIndex: "players",
    key: "players",
    width: 120,
    render: (count: number) => (
      <div style={{
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "8px 12px",
        background: "rgba(16, 185, 129, 0.1)",
        borderRadius: "20px",
        justifyContent: "center",
      }}>
        <UserOutlined style={{ color: "#10b981", fontSize: "14px" }} />
        <Text style={{
          color: "#10b981",
          fontSize: "16px",
          fontWeight: 700,
        }}>
          {count || 0}
        </Text>
      </div>
    ),
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: boolean) => (
      <Tag
        color={status ? "green" : "red"}
        style={{
          borderRadius: "12px",
          padding: "4px 12px",
          fontSize: "12px",
          fontWeight: 600,
          border: "none",
        }}
      >
        {status ? "Active" : "Inactive"}
      </Tag>
    ),
  },
  {
    title: "Actions",
    key: "actions",
    width: 100,
    render: (_: unknown, record: Tenant) => (
      <Button
        type="primary"
        icon={<EyeFilled />}
        style={{
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          border: "none",
          borderRadius: "8px",
          height: "36px",
          width: "36px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          boxShadow: "0 2px 8px rgba(102, 126, 234, 0.3)",
          transition: "all 0.3s ease",
        }}
        onMouseEnter={(e) => {
          const target = e.target as HTMLElement;
          target.style.transform = "translateY(-2px)";
          target.style.boxShadow = "0 4px 12px rgba(102, 126, 234, 0.4)";
        }}
        onMouseLeave={(e) => {
          const target = e.target as HTMLElement;
          target.style.transform = "translateY(0)";
          target.style.boxShadow = "0 2px 8px rgba(102, 126, 234, 0.3)";
        }}
        onClick={onViewTenant ? () => onViewTenant(record) : undefined}
      />
    ),
  },
];