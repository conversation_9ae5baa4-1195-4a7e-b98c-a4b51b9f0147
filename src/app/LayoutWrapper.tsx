"use client";
import { usePathname } from "next/navigation";
import MainLayout from "./MainLayout";

const AUTH_ROUTES = ["/login", "/register", "/forgot-password"];

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  if (AUTH_ROUTES.includes(pathname)) {
    return <>{children}</>;
  }

  return <MainLayout>{children}</MainLayout>;
} 