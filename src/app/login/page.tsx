"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Alert,
  // Checkbox,
  Card,
  Typography,
  Space,
} from "antd";
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import { supabaseClient } from "@/utils/supabase/client";
import { dataProviderInstance } from "@providers/data-provider";

const { Title, Text } = Typography;

export default function CustomSignIn() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [form] = Form.useForm();
  const router = useRouter();

  // Pre-fill email if remembered
  React.useEffect(() => {
    const rememberedEmail = localStorage.getItem("rememberedEmail");
    if (rememberedEmail) {
      form.setFieldsValue({ email: rememberedEmail, remember: true });
    }
  }, [form]);

  const onFinish = async (values: {
    email: string;
    password: string;
    remember?: boolean;
  }) => {
    setLoading(true);
    setError(null);
    const { data, error } = await supabaseClient.auth.signInWithPassword({
      email: values.email,
      password: values.password,
    });
    if (data.user?.id) {
      dataProviderInstance
        .getUserInfo(data.user.id)
        .then((response) => {
          const userRole = response.data.data.party_type_key;
          const userRoleId = response.data.data.id;
          const userId = response.data.data.id;
          const tenantId = response.data.data.tenant_id;
          const tenantCode = response.data.data.tenant_code;
          const parentPartyId = response.data.data.parent_party_id;
           // Store values in localStorage
          localStorage.setItem("role ", userRole);
          localStorage.setItem("userRoleId", userRoleId);
          localStorage.setItem("party_id", userId);
          localStorage.setItem("tenant_id", tenantId);
          localStorage.setItem("tenant_code", tenantCode);
          localStorage.setItem("parent_party_id", parentPartyId);
          
          if (userRole === "SUPERADMIN") {
            router.push("/dashboard");
          } else if (userRole === "TENANTADM") {
            router.push("tenants/dashboard");
          } else if (userRole === "CAREGIVER") {
             router.push("caregiver/dashboard");
          }
        })
        .catch((error) => {
          console.error("Error fetching user info:", error);
        });
    }
    // Remember Me logic
    const SUPABASE_PROJECT_REF = process.env.NEXT_PUBLIC_SUPABASE_KEY;
    const SUPABASE_SESSION_KEY = `sb-${SUPABASE_PROJECT_REF}-auth-token`;
    if (values.remember) {
      localStorage.setItem("rememberedEmail", values.email);
    } else {
      localStorage.removeItem("rememberedEmail");
      // Move session to sessionStorage
      const session = localStorage.getItem(SUPABASE_SESSION_KEY);
      if (session) {
        sessionStorage.setItem(SUPABASE_SESSION_KEY, session);
        // localStorage.removeItem(SUPABASE_SESSION_KEY);
      }
    }
    localStorage.setItem("user", JSON.stringify(data.user));
    setLoading(false);

    if (error) {
      setError(error.message);
    } else if (data?.session) {
      router.push("/");
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        padding: "24px",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: "-50%",
          left: "-50%",
          width: "200%",
          height: "200%",
          background: "radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",
          backgroundSize: "50px 50px",
          animation: "float 20s ease-in-out infinite",
        }}
      />

      <Card
        className="login-card"
        style={{
          width: "100%",
          maxWidth: "420px",
          boxShadow: "0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.1)",
          borderRadius: "16px",
          border: "none",
          background: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          position: "relative",
          zIndex: 1,
        }}
      >
        <Space direction="vertical" size="large" style={{ width: "100%", padding: "8px" }}>
          {/* Brand Header */}
          <div style={{ textAlign: "center", marginBottom: "8px" }}>
            <div
              style={{
                width: "120px",
                height: "120px",
                margin: "0 auto 20px",
                borderRadius: "50%",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 8px 32px rgba(102, 126, 234, 0.3)",
                position: "relative",
                overflow: "hidden",
              }}
            >
              <img
                src="/assets/logo1.png"
                alt="RecallLoop Logo"
                style={{
                  width: "80px",
                  height: "80px",
                  borderRadius: "50%",
                  objectFit: "cover",
                  filter: "brightness(1.1)",
                }}
              />
            </div>
            <Title
              level={2}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                fontWeight: 700,
                marginBottom: "8px",
                fontSize: "28px",
                letterSpacing: "-0.5px",
              }}
            >
              RecallLoop
            </Title>
            <Text
              style={{
                fontSize: "16px",
                color: "#6b7280",
                fontWeight: 500,
              }}
            >
              Welcome back! Please sign in to continue
            </Text>
          </div>

          {/* Login Form */}
          <Form
            form={form}
            name="login"
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
            size="large"
            style={{ marginTop: "8px" }}
          >
            <Form.Item
              label={
                <span style={{
                  color: "#374151",
                  fontWeight: 600,
                  fontSize: "14px"
                }}>
                  Email Address
                </span>
              }
              name="email"
              rules={[
                { required: true, message: "Please input your email!" },
                { type: "email", message: "Please enter a valid email!" },
              ]}
              style={{ marginBottom: "20px" }}
            >
              <Input
                prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
                placeholder="Enter your email address"
                style={{
                  borderRadius: "12px",
                  height: "48px",
                  border: "2px solid #e5e7eb",
                  fontSize: "16px",
                  transition: "all 0.3s ease",
                }}
                onFocus={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#667eea";
                  target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                }}
                onBlur={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#e5e7eb";
                  target.style.boxShadow = "none";
                }}
              />
            </Form.Item>

            <Form.Item
              label={
                <span style={{
                  color: "#374151",
                  fontWeight: 600,
                  fontSize: "14px"
                }}>
                  Password
                </span>
              }
              name="password"
              rules={[
                { required: true, message: "Please input your password!" },
              ]}
              style={{ marginBottom: "24px" }}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
                placeholder="Enter your password"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                style={{
                  borderRadius: "12px",
                  height: "48px",
                  border: "2px solid #e5e7eb",
                  fontSize: "16px",
                  transition: "all 0.3s ease",
                }}
                onFocus={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#667eea";
                  target.style.boxShadow = "0 0 0 3px rgba(102, 126, 234, 0.1)";
                }}
                onBlur={(e) => {
                  const target = e.target as HTMLInputElement;
                  target.style.borderColor = "#e5e7eb";
                  target.style.boxShadow = "none";
                }}
              />
            </Form.Item>

            {/* <Form.Item>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>Remember me</Checkbox>
                </Form.Item>
                <Button
              type="link"
              style={{ color: "#1890ff", padding: 0, height: "auto" }}
              onClick={() => router.push("/forgot-password")}
            >
              Forgot password?
            </Button>
              </div>
            </Form.Item> */}

            {error && (
              <Form.Item style={{ marginBottom: "20px" }}>
                <Alert
                  message={error}
                  type="error"
                  showIcon
                  style={{
                    borderRadius: "12px",
                    border: "none",
                    backgroundColor: "#fef2f2",
                    borderLeft: "4px solid #ef4444",
                  }}
                />
              </Form.Item>
            )}

            <Form.Item style={{ marginBottom: "24px" }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{
                  borderRadius: "12px",
                  height: "48px",
                  fontSize: "16px",
                  fontWeight: 600,
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                  boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(-2px)";
                  target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = "translateY(0)";
                  target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
                }}
              >
                {loading ? "Signing in..." : "Sign In"}
              </Button>
            </Form.Item>
          </Form>

          {/* Footer */}
          <div style={{
            textAlign: "center",
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid #e5e7eb",
          }}>
            <Text style={{
              fontSize: "14px",
              color: "#6b7280",
            }}>
              Don&apos;t have an account?{" "}
            </Text>
            <Button
              type="link"
              style={{
                fontSize: "14px",
                fontWeight: 600,
                padding: 0,
                height: "auto",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                textDecoration: "none",
              }}
              onClick={() => router.push("/register")}
            >
              Create Account
            </Button>
          </div>
        </Space>
      </Card>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .login-card {
          animation: fadeInUp 0.6s ease-out;
        }
      `}</style>
    </div>
  );
}
