"use client";
import React, { useState, useEffect } from "react";
import { Row, Col, Button, Input, Spin, message } from "antd";
import UserCard from "@components/card/userCards/userCard";

import { dataProviderInstance } from "@providers/data-provider";
import { PLAYER } from "@utils/supabase/constants";
import { ERROR_MESSAGES } from "@utils/supabase/messages";
import StatCard from "@components/card/graphCards/StatCard";

export type GetUserInfoResponse = {
  data: {
    tenant_code: string | null;
    id: string;
    party_type_key: string;
    parent_party_id: string;
  };
  success: boolean;
};

type Player = {
  id: string;
  name: string;
  email?: string | null;
  avatar_url?: string | null;
  type: string;
};

type DashboardData = {
  overview: {
    total_users: { count: number };
    total_players: { count: number };
    total_caregivers: { count: number };
  };
};

const Dashboard: React.FC = () => {
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(true);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [userData, setUserData] = useState<GetUserInfoResponse | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );

  useEffect(() => {
    const fetchPlayers = async () => {
      try {
        setLoading(true);
        const party_id = localStorage.getItem("party_id") || "";
        //const party_type_key = localStorage.getItem("role") || "";
        const tenant_code = localStorage.getItem("tenant_code") || "";
        const inputPara = {
          party_id: party_id,
          tenant_code: tenant_code,
          party_type_key: "CAREGIVER",
        };
        const response = await dataProviderInstance.listTenantUsers(inputPara);
        const playersList: Player[] = (response.data.data.players || []).map(
          (p: Player) => ({
            id: p.id,
            name: p.name,
            email: p.email,
            avatar_url: p.avatar_url,
            type: PLAYER,
          })
        );
        setPlayers(playersList);
      } catch (error) {
        console.error(error);
        message.error(ERROR_MESSAGES.load_games);
      } finally {
        setLoading(false);
      }
    };
    fetchPlayers();
  }, []);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const { data: user } = await dataProviderInstance.getUser();
        if (user?.id) {
          setCurrentUserId(user.id);
        }
      } catch (error) {
        console.error("Error getting current user:", error);
      }
    };
    fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (!currentUserId) return;
    const fetchUserInfo = async () => {
      try {
        const { data } = await dataProviderInstance.getUserInfo(currentUserId);
        if (data) {
          setUserData(data);
        }
      } catch (error) {
        console.error("Error fetching user info:", error);
        message.error("Failed to load user info");
      }
    };
    fetchUserInfo();
  }, [currentUserId]);

  useEffect(() => {
    if (!userData) return;
    console.log("userdata");
    console.log(userData);
    const fetchDashboardData = async () => {
      try {
        setDashboardLoading(true);
        const response = await dataProviderInstance.getUserDashboard({
          tenant_id: userData.data.parent_party_id,
          party_id: userData.data.id,
          party_type_key: userData.data.party_type_key,
        });
        setDashboardData(response.data.data);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        message.error("Failed to load dashboard statistics");
      } finally {
        setDashboardLoading(false);
      }
    };
    fetchDashboardData();
  }, [userData]);

  const filteredPlayers = players.filter(
    (player) =>
      player.name?.toLowerCase().includes(search.toLowerCase()) ||
      player.email?.toLowerCase().includes(search.toLowerCase())
  );

  const getStatCards = () => {
    const overview = dashboardData?.overview;

    const totalUsers = overview?.total_users?.count;
    const totalPlayers = overview?.total_players?.count;
    const totalCaregivers = overview?.total_caregivers?.count;

    console.log("=== StatCard Debug Info ===");
    console.log("totalUsers count:", totalUsers);
    console.log("totalPlayers count:", totalPlayers);
    console.log("totalCaregivers count:", totalCaregivers);
    console.log("Raw overview data:", overview);

    return [
      {
        title: "Active users",
        value: totalUsers,
        percentage: 12,
        status: "up" as const,
        color: "#10b981",
        chartType: "progress" as const,

        unit: "",
      },
      {
        title: "Games Completed",
        value: totalPlayers,
        percentage: 8,
        status: "up" as const,
        color: "#3b82f6",
        chartType: "line" as const,

        unit: "",
      },
      {
        title: "Workshops Attended",
        value: 0,
        percentage: 5,
        status: "up" as const,
        color: "#f59e0b",
        chartType: "progress" as const,

        unit: "",
      },
    ];
  };

  const statCards = getStatCards();

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "30px auto",
        padding: "40px 48px 0 48px",
        background: "#fff",
        minHeight: "100vh",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 32,
        }}
      >
        <Button
          type="primary"
          style={{
            background: "#7c5cfc",
            borderColor: "#7c5cfc",
            borderRadius: 8,
            fontWeight: 500,
            fontSize: 16,
            height: 40,
            padding: "0 28px",
          }}
          onClick={() => (window.location.href = "/caregiver/userManagement")}
        >
          Create New User
        </Button>
        <Input.Search
          placeholder="Search by name or email..."
          allowClear
          style={{ width: 260, borderRadius: 8 }}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {loading ? (
        <div style={{ textAlign: "center", padding: "60px 0" }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: "#666" }}>Loading players...</div>
        </div>
      ) : filteredPlayers.length === 0 ? (
        <div style={{ textAlign: "center", padding: "60px 0", color: "#999" }}>
          {search
            ? "No players found matching your search."
            : "No players available."}
        </div>
      ) : (
        <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
          {filteredPlayers.map((player) => (
            <Col xs={24} sm={12} md={8} lg={8} xl={8} key={player.id}>
              <UserCard
                id={player.id}
                name={player.name || "No Name"}
                title={player.email || "No Email"}
                location=""
                avatar={player.avatar_url || undefined}
              />
            </Col>
          ))}
        </Row>
      )}

      {dashboardLoading ? (
        <div style={{ textAlign: "center", padding: "40px 0" }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: "#666" }}>
            Loading statistics...
          </div>
        </div>
      ) : (
        <Row gutter={[24, 24]} style={{ marginTop: 8 }}>
          {statCards.map((card, index) => (
            <Col
              xs={24}
              sm={24}
              md={8}
              lg={8}
              xl={8}
              key={`${card.title}-${index}`}
            >
              <div style={{ maxWidth: 340, minWidth: 260, margin: "0 auto" }}>
                <StatCard {...card} value={card.value ?? 0} />
              </div>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
