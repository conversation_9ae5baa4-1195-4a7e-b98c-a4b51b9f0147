"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  Typography,
  DatePicker,
  Select,
  Upload,
  Switch,
  Row,
  Col,
  message,
  Spin,
} from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { dataProviderInstance } from "@/providers/data-provider"; // Adjust the import path as needed
import { useNotification } from "@refinedev/core";
import { ManageUserFormValues, Game } from "@types";
import { supabaseClient } from "@utils/supabase/client";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@utils/supabase/messages";
import dayjs from "dayjs";
const { Title, Text } = Typography;
const { TextArea } = Input;
export default function UserCreatePage() {
  const [form] = Form.useForm();
  // const [loginRequired, setLoginRequired] = useState(true);
  const [loginRequired] = useState(true);
  const { open } = useNotification();
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [gameOptions, setGameOptions] = useState<Game[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);
  const [gameNotes, setGameNotes] = useState<string>("");
  const [gameDueDate, setGameDueDate] = useState<string>("");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [userRoleId, setUserRoleId] = useState<string>("");
  const [selectedRoleName, setSelectedRoleName] = useState<string>("");
  const [selectedGameId, setSelectedGameId] = useState<string>("");

  const tenantCode = localStorage.getItem("tenant_code");
  const party_id = localStorage.getItem("party_id");
  const tenant_id = localStorage.getItem("tenant_id");

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await dataProviderInstance.listRoles();
        const playerRole = response.data.find(
          (role: { role_name: string }) =>
            role.role_name.toLowerCase() === "player"
        );
        if (playerRole) {
          setUserRoleId(playerRole.id);
          setSelectedRoleName(playerRole.role_name);
          console.log(selectedRoleName);
        }
      } catch (error) {
        console.error("Failed to fetch roles:", error);
      }
    };
    fetchRoles();
    //setLoginRequired(true);
  }, []);
  const fetchUnassignedGames = useCallback(async () => {
    if (!party_id) return;

    try {
      setLoadingGames(true);
      console.log("🎮 Fetching unassigned games for party_id:", party_id);

      const response = await dataProviderInstance.listUnAssignedGames({
        tenant_id: tenant_id || "",
        party_id: party_id,
      });

      if (response && response.data) {
        const games = Array.isArray(response.data)
          ? response.data
          : response.data.data || [];
        setGameOptions(games);
        console.log("✅ Unassigned games fetched:", games);
      } else {
        setGameOptions([]);
      }
    } catch (error) {
      console.error("❌ Error fetching unassigned games:", error);
      message.error(ERROR_MESSAGES.load_games);
      setGameOptions([]);
    } finally {
      setLoadingGames(false);
    }
  }, [party_id]);

  // Fetch unassigned games when party_id is available
  useEffect(() => {
    if (party_id) {
      fetchUnassignedGames();
    }
  }, [party_id, fetchUnassignedGames]);
  const handleFinish = async (values: ManageUserFormValues) => {
    let thumbnailUrl = uploadedImageUrl;
    if (imageFile) {
      const fileName = `players/${Date.now()}_${imageFile.name}`;
      const { error: uploadError } = await supabaseClient.storage
        .from("image-bucket")
        .upload(fileName, imageFile, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) {
        open?.({
          type: "error",
          message: "Failed to upload image.",
        });
        return;
      }

      const { data: signedUrlData, error: signedUrlError } =
        await supabaseClient.storage
          .from("image-bucket")
          .createSignedUrl(fileName, 60 * 60 * 157680000); // 1 hour expiry

      if (signedUrlData && signedUrlData.signedUrl) {
        thumbnailUrl = signedUrlData.signedUrl;
      } else {
        console.log(signedUrlError);
        throw new Error("Failed to retrieve signed URL for uploaded image.");
      }
    }

    try {
      let authUserId = null;
      // 1. Call signup API first if login is required
      if (loginRequired) {
        const signUpResponse = await dataProviderInstance.signUpUser({
          email: values.email!,
          password: values.password!,
          first_name: values.firstName!,
          last_name: values.lastName!,
          phone_number: `${values.countryCode!} ${values.phone!}`,
          username: values.username!,
          tenant_code: tenantCode!,
          party_id: party_id!,
          rolename: "PLAYER",
          avatar_url: null,
          party_type_key: "PLAYER",
        });
        if (!signUpResponse.data?.user?.id) {
          throw new Error("Failed to create user. No user ID returned.");
        }
        authUserId = signUpResponse.data.user.id;
        console.log("Auth user created:", authUserId);
      }
      // 3. Prepare params for manageUser, using partyId
      const params = {
        first_name: values.firstName as string,
        last_name: values.lastName as string,
        date_of_birth: values.dob
          ? typeof values.dob === "string"
            ? values.dob
            : values.dob.format("YYYY-MM-DD")
          : null,
        gender: values.gender as string,
        bio: values.bio as string,
        role_id: userRoleId ?? "",
        phone_number: `${values.countryCode} ${values.phone}`,
        tenant_code: tenantCode ?? "",
        profile_url: thumbnailUrl as string,
        is_login_enabled: loginRequired,
        party_id: authUserId ?? "",
        party_type_key: "PLAYER",
      };

      // 4. Call manageUser API
      const result = await dataProviderInstance.manageUser(params);
      // Update auth user metadata if login was enabled
      // if (authUserId && loginRequired) {
      //   const { error: updateError } = await supabaseClient.auth.updateUser({
      //     data: {
      //       role_id: userRoleId,
      //       role_name: 'PLAYER',
      //       party_id: party_id,
      //       tenant_code: tenantCode,
      //     },
      //   });

      //   if (updateError) {
      //     console.error("User update error:", updateError);
      //     // Don't throw here as the main user creation was successful
      //     console.warn(
      //       "Failed to update user metadata, but player was created successfully"
      //     );
      //   } else {
      //     console.log("User metadata updated successfully");
      //   }
      // }
      // Success notification will be shown after game assignment (if any)
      if (!selectedGameId) {
        // Only show this if no game is being assigned
        open?.({
          type: "success",
          message: "PLAYER Created Successfully!",
        });
      }
      // Auto-assign game if one is selected
      if (selectedGameId && authUserId) {
        try {
          await dataProviderInstance.assignGame({
            // party_ids: authUserId ? [authUserId] : [],
            // game_id: selectedGameId,
            // due_date: gameDueDate || undefined,
            // notes: gameNotes || undefined,
            due_date: gameDueDate || null,
            game_ids: selectedGameId ? [selectedGameId] : ([] as string[]),
            notes: gameNotes || null,
            party_ids: authUserId ? [authUserId] : [],
          });
          message.success("Player created and game assigned successfully!");
          // Reset game assignment form
          setSelectedGameId("");
          setGameDueDate("");
          setGameNotes("");
          // Refresh unassigned games list
          fetchUnassignedGames();
        } catch (gameError) {
          console.error("❌ Error assigning game:", gameError);
          message.error(
            "Player created successfully, but failed to assign game"
          );
        }
      } else {
        console.log("ℹ️ No game selected, skipping game assignment");
      }
      // Reset form
      form.resetFields();
      open?.({
        type: "success",
        message: SUCCESS_MESSAGES.create_player,
      });
      console.log("Player created:", result.data);
      //router.push("/dashboard");
    } catch (error) {
      open?.({
        type: "error",
        message: ERROR_MESSAGES.create_player,
      });
      console.error("Failed to create player:", error);
    }
  };
  const uploadProps = {
    beforeUpload: (file: File) => {
      const isImage = file.type.startsWith("image/");
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        message.error(ERROR_MESSAGES.validate_image);
        return false;
      }

      if (!isLt5M) {
        message.error(ERROR_MESSAGES.validate_size);
        return false;
      }

      const previewUrl = URL.createObjectURL(file);
      setUploadedImageUrl(previewUrl);
      setImageFile(file);
      return false;
    },
    showUploadList: false,
  };

  return (
    <div
      style={{
        maxWidth: 1280,
        margin: "24px auto 0",
        background: "#fff",
        borderRadius: 12,
        boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
        padding: 32,
      }}
    >
      <div>
        <Title
          level={2}
          style={{ textAlign: "center", marginBottom: 32, fontWeight: 700 }}
        >
          Player Information
        </Title>
        <Form
          layout="vertical"
          style={{ margin: "0 auto", maxWidth: 1000 }}
          onFinish={handleFinish}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="firstName"
                label="First Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "First Name is required" }]}
              >
                <Input size="large" placeholder="First Name" />
              </Form.Item>
              <Form.Item
                name="dob"
                label="Date of Birth"
                style={{ marginBottom: 16 }}
                rules={[
                  { required: true, message: "Date of Birth is required" },
                ]}
              >
                <DatePicker
                  size="large"
                  style={{ width: "100%" }}
                  format="YYYY-MM-DD"
                  disabledDate={(current) =>
                    current &&
                    current > dayjs().subtract(18, "year").endOf("day")
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="lastName"
                label="Last Name"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Last Name is required" }]}
              >
                <Input size="large" placeholder="Last Name" />
              </Form.Item>
              <Form.Item
                name="gender"
                label="Gender"
                style={{ marginBottom: 16 }}
                rules={[{ required: true, message: "Gender is required" }]}
              >
                <Select size="large" placeholder="Select Gender">
                  <Select.Option value="male">Male</Select.Option>
                  <Select.Option value="female">Female</Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label="Phone Number"
                style={{ marginBottom: 24 }}
                required
              >
                <div style={{ display: "flex" }}>
                  <Form.Item
                    name="countryCode"
                    noStyle
                    rules={[
                      { required: true, message: "Country code is required" },
                    ]}
                    initialValue="+91"
                  >
                    <Select
                      size="large"
                      style={{
                        width: 120,
                        borderRadius: "6px 0 0 6px",
                        fontSize: 16,
                      }}
                    >
                      <Select.Option value="+1">+1 (US)</Select.Option>
                      <Select.Option value="+44">+44 (UK)</Select.Option>
                      <Select.Option value="+91">+91 (IN)</Select.Option>
                      <Select.Option value="+61">+61 (AU)</Select.Option>
                      <Select.Option value="+81">+81 (JP)</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    name="phone"
                    noStyle
                    rules={[
                      { required: true, message: "Phone number is required" },
                      {
                        pattern: /^\d{7,14}$/,
                        message: "Enter a valid phone number (7-14 digits)",
                      },
                    ]}
                  >
                    <Input
                      size="large"
                      style={{
                        width: "calc(100% - 120px)",
                        borderRadius: "0 6px 6px 0",
                      }}
                      placeholder="Phone Number"
                      maxLength={14}
                      minLength={7}
                      type="tel"
                    />
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="profileImage"
                label="Profile Image"
                style={{ marginBottom: 16 }}
              >
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>Upload</Button>
                </Upload>
                {/* Show progress bar if image is uploading */}
                {imageFile && !uploadedImageUrl && (
                  <div style={{ marginTop: 8 }}>
                    <div
                      style={{
                        width: "100%",
                        background: "#f0f0f0",
                        borderRadius: 4,
                        height: 8,
                        overflow: "hidden",
                      }}
                    >
                      <div
                        style={{
                          width: "100%",
                          height: "100%",
                          background: "#6366f1",
                          animation: "progressBar 1.2s linear infinite",
                        }}
                      />
                    </div>
                    <style>
                      {`
                        @keyframes progressBar {
                          0% { width: 0%; }
                          100% { width: 100%; }
                        }
                        `}
                    </style>
                  </div>
                )}
                {/* Show preview if uploaded */}
                {uploadedImageUrl && (
                  <div
                    style={{
                      marginTop: 12,
                      display: "flex",
                      justifyContent: "flex-start",
                    }}
                  >
                    <div
                      style={{
                        width: 128,
                        height: 128,
                        border: "2px solid #d1d5db",
                        borderRadius: 8,
                        overflow: "hidden",
                        backgroundColor: "#f9fafb",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                      }}
                    >
                      <img
                        src={uploadedImageUrl}
                        alt="Profile Preview"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                    </div>
                  </div>
                )}
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="Login Required" style={{ marginBottom: 16 }}>
            {/* <Switch checked={loginRequired} onChange={setLoginRequired} /> */}
            <Switch
              checked={true}
              disabled={true}
              //onChange={setLoginRequired}
            />
          </Form.Item>
          {loginRequired && (
            <Row gutter={24}>
              <Col xs={24} md={8}>
                <Form.Item
                  name="username"
                  label="Username"
                  style={{ marginBottom: 16 }}
                  rules={[{ required: true, message: "Username is required" }]}
                >
                  <Input size="large" placeholder="Username" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  name="email"
                  label="Email"
                  style={{ marginBottom: 16 }}
                  rules={[
                    { required: true, message: "Email is required" },
                    {
                      type: "email",
                      message: "Please enter a valid email address",
                    },
                  ]}
                >
                  <Input size="large" placeholder="Email" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  name="password"
                  label="Password"
                  style={{ marginBottom: 16 }}
                  rules={[{ required: true, message: "Password is required" }]}
                >
                  <Input.Password size="large" placeholder="Password" />
                </Form.Item>
              </Col>
            </Row>
          )}
          <Col xs={24}>
            <Form.Item
              label="Player Bio"
              name="bio"
              rules={[{ required: true, message: "Please enter player bio" }]}
              style={{ marginBottom: 0 }}
            >
              <Input.TextArea placeholder="Add bio..." rows={4} />
            </Form.Item>
          </Col>
          <div
            style={{
              marginTop: "32px",
              paddingTop: "24px",
              borderTop: "1px solid #f0f0f0",
            }}
          >
            <Typography.Title level={4} style={{ marginBottom: "8px" }}>
              Game Assignment (Optional)
            </Typography.Title>
            <Typography.Text
              type="secondary"
              style={{ marginBottom: "24px", display: "block" }}
            >
              Select a game to automatically assign to the player after creation
            </Typography.Text>

            <Row gutter={16}>
              <Col xs={24} md={24}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Select Game
                  </label>
                  <Select
                    size="large"
                    placeholder="Select a game to assign"
                    value={selectedGameId}
                    onChange={(value) => {
                      console.log("🎮 Game selected:", value);
                      setSelectedGameId(value);
                    }}
                    loading={loadingGames}
                    style={{ width: "100%" }}
                    options={gameOptions.map((game) => ({
                      label: game.game_name,
                      value: game.game_id,
                    }))}
                    notFoundContent={
                      loadingGames ? (
                        <Spin size="small" />
                      ) : (
                        "No games available"
                      )
                    }
                  />
                </div>
              </Col>

              <Col xs={24} md={5}>
                <div style={{ marginBottom: 16 }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Due Date
                  </label>
                  <DatePicker
                    size="large"
                    style={{ width: "100%" }}
                    placeholder="Select due date"
                    value={gameDueDate ? dayjs(gameDueDate) : null}
                    onChange={(_, dateString) => {
                      if (typeof dateString === "string")
                        setGameDueDate(dateString);
                      else setGameDueDate("");
                    }}
                    disabledDate={(current) =>
                      current && current < dayjs().startOf("day")
                    }
                  />
                </div>
              </Col>
              <Col xs={24} sm={19}>
                <div>
                  <Text
                    style={{
                      display: "block",
                      marginBottom: 8,
                      fontWeight: 500,
                    }}
                  >
                    Notes <span style={{ color: "red" }}>*</span>
                  </Text>
                  <TextArea
                    placeholder="Enter notes"
                    value={gameNotes}
                    onChange={(e) => setGameNotes(e.target.value)}
                    rows={4}
                    style={{ width: "100%" }}
                  />
                </div>
              </Col>
            </Row>
          </div>
          <div style={{ textAlign: "center", marginTop: 40 }}>
            <Button
              type="primary"
              size="large"
              style={{
                background: "#6366f1",
                border: "none",
                borderRadius: 8,
                minWidth: 180,
                fontWeight: 500,
                fontSize: 16,
              }}
              htmlType="submit"
            >
              Submit
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
}
