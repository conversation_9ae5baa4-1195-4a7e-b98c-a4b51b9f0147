"use client";
import React, { useState, useEffect } from "react";
import { Row, Col, <PERSON>po<PERSON>, Button, Spin, message, Layout } from "antd";
import GameCard from "@components/card/gameCards/page";
import { PlusOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { dataProviderInstance } from "@providers/data-provider";
import { Game, FetchGamesResponse } from "@types";

const { Title, Text } = Typography;
const { Content } = Layout;

export default function HomePage() {
  const router = useRouter();
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGames();
  }, []);

  const fetchGames = async () => {
    try {
      setLoading(true);
      const response: FetchGamesResponse =
        await dataProviderInstance.listGames();
      setGames(response.data || []);
    } catch (error) {
      console.error("Error fetching games:", error);
      message.error("Failed to load games");
      setGames([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Background decorative elements */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "200px",
          background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
          borderRadius: "0 0 50% 50%",
          transform: "scale(1.2)",
          zIndex: 0,
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "-30px",
          right: "-30px",
          width: "150px",
          height: "150px",
          background: "radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          zIndex: 0,
        }}
      />

      <Content
        style={{
          maxWidth: 1400,
          margin: "0 auto",
          padding: "40px 20px",
          position: "relative",
          zIndex: 1,
        }}
      >
        {/* Header Section */}
        <div style={{
          textAlign: "center",
          marginBottom: "48px",
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(10px)",
          borderRadius: "24px",
          padding: "40px 32px",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        }}>
          <Title
            level={1}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontWeight: 700,
              marginBottom: "16px",
              fontSize: "42px",
              letterSpacing: "-1px",
            }}
          >
            Game Management
          </Title>
          <Text
            style={{
              color: "#6b7280",
              fontSize: "18px",
              fontWeight: 500,
              display: "block",
              marginBottom: "32px",
              lineHeight: "1.6",
            }}
          >
            Create, manage, and organize your games collection
          </Text>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="large"
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              border: "none",
              height: "48px",
              paddingLeft: "24px",
              paddingRight: "24px",
              fontSize: "16px",
              fontWeight: 600,
              borderRadius: "12px",
              boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
              transition: "all 0.3s ease",
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLElement;
              target.style.transform = "translateY(-2px)";
              target.style.boxShadow = "0 8px 25px rgba(102, 126, 234, 0.5)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLElement;
              target.style.transform = "translateY(0)";
              target.style.boxShadow = "0 4px 15px rgba(102, 126, 234, 0.4)";
            }}
            onClick={() => router.push("/games/create")}
          >
            Add New Game
          </Button>
        </div>

        {/* Games Content */}
        {loading ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "400px",
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
            }}
          >
            <Spin size="large" />
            <Text style={{
              color: "#6b7280",
              fontSize: "16px",
              fontWeight: 500,
              marginTop: "16px",
            }}>
              Loading games...
            </Text>
          </div>
        ) : games.length === 0 ? (
          <div
            style={{
              textAlign: "center",
              padding: "80px 40px",
              background: "rgba(255, 255, 255, 0.8)",
              backdropFilter: "blur(10px)",
              borderRadius: "20px",
              boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
            }}
          >
            <div
              style={{
                width: "80px",
                height: "80px",
                margin: "0 auto 24px",
                borderRadius: "50%",
                background: "linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <PlusOutlined style={{ fontSize: "32px", color: "#667eea" }} />
            </div>
            <Title level={3} style={{
              color: "#374151",
              marginBottom: "16px",
            }}>
              No Games Found
            </Title>
            <Text style={{
              color: "#6b7280",
              fontSize: "16px",
              display: "block",
              marginBottom: "24px",
            }}>
              Get started by creating your first game!
            </Text>
            <Button
              type="primary"
              size="large"
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                borderRadius: "12px",
                height: "48px",
                paddingLeft: "24px",
                paddingRight: "24px",
                fontSize: "16px",
                fontWeight: 600,
              }}
              onClick={() => router.push("/games/create")}
            >
              Create First Game
            </Button>
          </div>
        ) : (
          <div>
            <div style={{
              marginBottom: "32px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}>
              <Title level={3} style={{
                color: "#374151",
                margin: 0,
                fontSize: "24px",
                fontWeight: 600,
              }}>
                Your Games ({games.length})
              </Title>
            </div>
            <Row gutter={[32, 32]}>
              {games.map((game, index) => (
                <Col key={game.game_id} xs={24} sm={12} lg={8} xl={6}>
                  <div
                    style={{
                      animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`,
                    }}
                  >
                    <GameCard
                      id={game.game_id}
                      image={game.thumbnail_url}
                      title={game.game_name}
                      description={game.description}
                      players={game.no_of_levels || 0}
                      category_name={game.category_name}
                    />
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        )}
      </Content>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </Layout>
  );
}
